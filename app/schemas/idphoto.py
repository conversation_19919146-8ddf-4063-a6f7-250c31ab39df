"""
证件照相关的数据模式
"""
from pydantic import BaseModel, Field
from typing import Optional, Union
from enum import Enum

class IDPhotoSize(str, Enum):
    """证件照尺寸枚举"""
    ONE_INCH = "one_inch"
    TWO_INCH = "two_inch"
    BIG_ONE_INCH = "big_one_inch"
    SMALL_ONE_INCH = "small_one_inch"
    BIG_TWO_INCH = "big_two_inch"
    SMALL_TWO_INCH = "small_two_inch"

class IDPhotoColor(str, Enum):
    """证件照颜色枚举"""
    TRANSPARENT = "transparent"
    WHITE = "white"
    BLUE = "blue"
    RED = "red"
    BLUE_GRADIENT = "blue_gradient"
    RED_GRADIENT = "red_gradient"

# 尺寸配置映射（包含中文显示名称、像素尺寸和冲印尺寸）
IDPHOTO_SIZE_CONFIG = {
    IDPhotoSize.ONE_INCH: {
        "name": "一寸",
        "width": 295,
        "height": 413,
        "print_size": "2.5cm*3.5cm",
        "description": "标准一寸证件照"
    },
    IDPhotoSize.TWO_INCH: {
        "name": "二寸",
        "width": 413,
        "height": 579,
        "print_size": "3.5cm*4.9cm",
        "description": "标准二寸证件照"
    },
    IDPhotoSize.BIG_ONE_INCH: {
        "name": "大一寸",
        "width": 390,
        "height": 567,
        "print_size": "3.3cm*4.8cm",
        "description": "大一寸证件照"
    },
    IDPhotoSize.SMALL_ONE_INCH: {
        "name": "小一寸",
        "width": 259,
        "height": 377,
        "print_size": "2.2cm*3.2cm",
        "description": "小一寸证件照"
    },
    IDPhotoSize.BIG_TWO_INCH: {
        "name": "大二寸",
        "width": 413,
        "height": 626,
        "print_size": "3.5cm*5.3cm",
        "description": "大二寸证件照"
    },
    IDPhotoSize.SMALL_TWO_INCH: {
        "name": "小二寸",
        "width": 413,
        "height": 531,
        "print_size": "3.5cm*4.5cm",
        "description": "小二寸证件照"
    }
}

# 颜色配置映射（包含中文显示名称和渲染参数）
IDPHOTO_COLOR_CONFIG = {
    IDPhotoColor.TRANSPARENT: {
        "name": "透明",
        "hex": None,
        "render": 0,
        "description": "透明背景"
    },
    IDPhotoColor.WHITE: {
        "name": "白色",
        "hex": "FFFFFF",
        "render": 0,
        "description": "白色背景"
    },
    IDPhotoColor.BLUE: {
        "name": "蓝色",
        "hex": "438EDB",
        "render": 0,
        "description": "蓝色背景"
    },
    IDPhotoColor.RED: {
        "name": "红色",
        "hex": "FF0000",
        "render": 0,
        "description": "红色背景"
    },
    IDPhotoColor.BLUE_GRADIENT: {
        "name": "蓝色渐变",
        "hex": "438EDB",
        "render": 1,
        "description": "蓝色渐变背景"
    },
    IDPhotoColor.RED_GRADIENT: {
        "name": "红色渐变",
        "hex": "FF0000",
        "render": 1,
        "description": "红色渐变背景"
    }
}

class IDPhotoRequest(BaseModel):
    """证件照生成请求"""
    size: IDPhotoSize = Field(default=IDPhotoSize.ONE_INCH, description="证件照尺寸")
    color: IDPhotoColor = Field(default=IDPhotoColor.WHITE, description="背景颜色")

    class Config:
        use_enum_values = True

class IDPhotoResponse(BaseModel):
    """证件照生成响应"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    data: Optional[dict] = Field(None, description="响应数据")
    
    class Config:
        json_encoders = {
            # 可以添加自定义编码器
        }

class IDPhotoProcessRequest(BaseModel):
    """证件照处理请求（内部使用）"""
    input_image_base64: str = Field(..., description="输入图片的base64编码")
    width: int = Field(..., description="目标宽度")
    height: int = Field(..., description="目标高度")
    color: Optional[str] = Field(None, description="背景颜色HEX值")
    render: int = Field(default=0, description="渲染模式：0-纯色，1-上下渐变，2-中心渐变")
    
class IDPhotoProcessResponse(BaseModel):
    """证件照处理响应（内部使用）"""
    status: bool = Field(..., description="处理状态")
    image_base64_standard: Optional[str] = Field(None, description="标准证件照base64")
    image_base64_hd: Optional[str] = Field(None, description="高清证件照base64")
    image_base64: Optional[str] = Field(None, description="最终图片base64")
    error: Optional[str] = Field(None, description="错误信息")
