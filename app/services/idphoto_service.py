"""
证件照服务 - 调用后端证件照API的HTTP客户端
"""
import httpx
import logging
from typing import Optional, Dict, Any, Union
import base64
import io
from PIL import Image

from app.config import settings
from app.schemas.idphoto import IDPhotoProcessRequest, IDPhotoProcessResponse

# 配置日志
logger = logging.getLogger(__name__)

class IDPhotoService:
    """
    调用证件照API服务的客户端
    """

    def __init__(self, base_url: str = None):
        """
        初始化证件照服务客户端
        
        Args:
            base_url: 证件照API服务的基础URL
        """
        self.base_url = base_url or settings.IDPHOTO_SERVICE_URL
        self.idphoto_url = f"{self.base_url}/idphoto"
        self.add_background_url = f"{self.base_url}/add_background"
        logger.info(f"证件照服务初始化，服务URL: {self.base_url}")
        
    async def check_health(self) -> Dict[str, Any]:
        """
        检查证件照服务的健康状态
        
        Returns:
            字典包含健康状态信息
        """
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(f"{self.base_url}/health", timeout=5.0)
                response.raise_for_status()
                return {"status": "ok", "message": "证件照服务运行正常"}
        except Exception as e:
            logger.error(f"检查证件照服务健康状态失败: {str(e)}")
            return {"status": "error", "message": str(e)}
    
    def image_to_base64(self, image_file) -> str:
        """
        将上传的图片文件转换为base64编码
        
        Args:
            image_file: 上传的图片文件
            
        Returns:
            base64编码的图片字符串
        """
        try:
            # 读取图片内容
            image_content = image_file.read()
            # 重置文件指针
            image_file.seek(0)
            # 转换为base64
            base64_str = base64.b64encode(image_content).decode('utf-8')
            return base64_str
        except Exception as e:
            logger.error(f"图片转base64失败: {str(e)}")
            raise
    
    async def generate_transparent_idphoto(self, 
                                         image_base64: str,
                                         width: int,
                                         height: int) -> Dict[str, Any]:
        """
        生成透明背景的证件照
        
        Args:
            image_base64: 输入图片的base64编码
            width: 目标宽度
            height: 目标高度
            
        Returns:
            包含透明证件照的响应数据
        """
        try:
            # 准备请求数据
            data = {
                "input_image_base64": image_base64,
                "width": width,
                "height": height,
                "hd": True,
                "dpi": 300,
                "face_alignment": True
            }
            
            logger.info(f"正在生成透明证件照，尺寸: {width}x{height}")
            
            # 发送请求到证件照生成服务
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    self.idphoto_url,
                    data=data,
                    timeout=60.0,  # 较长的超时时间
                    headers={"Content-Type": "application/x-www-form-urlencoded"}
                )
                
                # 检查响应
                if response.status_code == 200:
                    result = response.json()
                    logger.info("透明证件照生成成功")
                    return result
                else:
                    logger.error(f"证件照服务返回错误: {response.status_code}, {response.text}")
                    return {"status": False, "error": f"服务错误: {response.status_code}"}
                    
        except Exception as e:
            logger.error(f"生成透明证件照失败: {str(e)}")
            return {"status": False, "error": str(e)}
    
    async def add_background_color(self,
                                 image_base64: str,
                                 color: str,
                                 render: int = 0) -> Dict[str, Any]:
        """
        为透明证件照添加背景色
        
        Args:
            image_base64: 透明证件照的base64编码
            color: 背景颜色HEX值
            render: 渲染模式 0-纯色，1-上下渐变，2-中心渐变
            
        Returns:
            包含最终证件照的响应数据
        """
        try:
            # 准备请求数据
            data = {
                "input_image_base64": image_base64,
                "color": color,
                "render": render,
                "dpi": 300
            }
            
            logger.info(f"正在添加背景色: {color}, 渲染模式: {render}")
            
            # 发送请求到背景色添加服务
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    self.add_background_url,
                    data=data,
                    timeout=60.0,
                    headers={"Content-Type": "application/x-www-form-urlencoded"}
                )
                
                # 检查响应
                if response.status_code == 200:
                    result = response.json()
                    logger.info("背景色添加成功")
                    return result
                else:
                    logger.error(f"背景色服务返回错误: {response.status_code}, {response.text}")
                    return {"status": False, "error": f"服务错误: {response.status_code}"}
                    
        except Exception as e:
            logger.error(f"添加背景色失败: {str(e)}")
            return {"status": False, "error": str(e)}
    
    async def generate_idphoto(self,
                             image_file,
                             size_config: Dict[str, int],
                             color_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成完整的证件照（包含背景色）
        
        Args:
            image_file: 上传的图片文件
            size_config: 尺寸配置 {"width": xxx, "height": xxx}
            color_config: 颜色配置 {"color": "FFFFFF", "render": 0}
            
        Returns:
            最终的证件照生成结果
        """
        try:
            # 转换图片为base64
            image_base64 = self.image_to_base64(image_file)
            
            # 第一步：生成透明背景证件照
            transparent_result = await self.generate_transparent_idphoto(
                image_base64,
                size_config["width"],
                size_config["height"]
            )
            
            if not transparent_result.get("status"):
                return transparent_result
            
            # 如果是透明背景，直接返回
            if color_config["color"] is None:
                return {
                    "status": True,
                    "image_base64": transparent_result.get("image_base64_standard"),
                    "image_base64_hd": transparent_result.get("image_base64_hd")
                }
            
            # 第二步：添加背景色
            background_result = await self.add_background_color(
                transparent_result.get("image_base64_standard"),
                color_config["color"],
                color_config["render"]
            )
            
            if not background_result.get("status"):
                return background_result
            
            return {
                "status": True,
                "image_base64": background_result.get("image_base64"),
                "transparent_base64": transparent_result.get("image_base64_standard"),
                "hd_base64": transparent_result.get("image_base64_hd")
            }
            
        except Exception as e:
            logger.error(f"生成证件照失败: {str(e)}")
            return {"status": False, "error": str(e)}

# 创建全局实例
idphoto_service = IDPhotoService()
